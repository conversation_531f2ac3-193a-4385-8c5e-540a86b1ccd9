//plugins {
//    id "com.android.application"
//    // START: FlutterFire Configuration
//    id 'com.google.gms.google-services'
//    // END: FlutterFire Configuration
//    id "kotlin-android"
//    id "dev.flutter.flutter-gradle-plugin"
//}
//
//def localProperties = new Properties()
//def localPropertiesFile = rootProject.file('local.properties')
//if (localPropertiesFile.exists()) {
//    localPropertiesFile.withReader('UTF-8') { reader ->
//        localProperties.load(reader)
//    }
//}
//
//def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
//if (flutterVersionCode == null) {
//    flutterVersionCode = '1'
//}
//
//def flutterVersionName = localProperties.getProperty('flutter.versionName')
//if (flutterVersionName == null) {
//    flutterVersionName = '1.0'
//}
//def keystoreProperties = new Properties()
//   def keystorePropertiesFile = rootProject.file('key.properties')
//   if (keystorePropertiesFile.exists()) {
//       keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
//   }
//android {
//       namespace "com.maarefa.moltaqa"
//    compileSdk 34
//    ndkVersion flutter.ndkVersion
//    packagingOptions {
//        jniLibs {
//            pickFirsts += ['**/*.so']
//        }
//    }
//
//
//    compileOptions {
//        coreLibraryDesugaringEnabled true
//        sourceCompatibility JavaVersion.VERSION_1_8
//        targetCompatibility JavaVersion.VERSION_1_8
//    }
//
//    kotlinOptions {
//        jvmTarget = '1.8'
//    }
//
//
//    sourceSets {
//        main.java.srcDirs += 'src/main/kotlin'
//    }
//
//    defaultConfig {
//        applicationId "com.maarefa.moltaqa"
//        minSdkVersion 26
//        targetSdkVersion 34
//        versionCode flutterVersionCode.toInteger()
//        versionName flutterVersionName
//        multiDexEnabled true
//
//    }
//    signingConfigs {
//       release {
//           keyAlias keystoreProperties['keyAlias']
//           keyPassword keystoreProperties['keyPassword']
//           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
//           storePassword keystoreProperties['storePassword']
//       }
//    }
//     signingConfigs {
//       release {
//           keyAlias keystoreProperties['keyAlias']
//           keyPassword keystoreProperties['keyPassword']
//           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
//           storePassword keystoreProperties['storePassword']
//       }
//   }
//    buildTypes {
//        release {
//            shrinkResources = false
//            minifyEnabled = false
//            signingConfig signingConfigs.release
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//        }
//        debug {
//            // shrinkResources = true
//            // minifyEnabled = true
//            signingConfig signingConfigs.debug
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//        }
//    }
//    lint {
//        abortOnError false
//        checkReleaseBuilds false
//        disable 'InvalidPackage', 'Instantiatable'
//    }
//}
//
//flutter {
//    source '../..'
//}
//
//dependencies {
//    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
//    implementation 'androidx.multidex:multidex:2.0.1'
//    implementation ('org.jitsi.react:jitsi-meet-sdk:5.0.2') { transitive = true }
//    implementation fileTree(dir: 'libs', include: ['*.jar'])
//    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
//    implementation platform('com.google.firebase:firebase-bom:31.2.0')
//    implementation 'androidx.appcompat:appcompat:1.6.1'
//    testImplementation 'junit:junit:4.13.2'
//    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
//    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
//    implementation 'androidx.work:work-runtime-ktx:2.8.1'
//    implementation 'com.google.firebase:firebase-iid:21.1.0'
//    implementation 'com.google.firebase:firebase-dynamic-links-ktx'
//    implementation 'com.google.firebase:firebase-analytics-ktx'
//    implementation 'com.google.android.gms:play-services-auth:20.5.0'
//}
plugins {
    id "com.android.application"
    id 'com.google.gms.google-services' // FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '1'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '1.0'

android {
    namespace "com.maarefa.moltaqa"
    compileSdk 34
    ndkVersion flutter.ndkVersion

    packagingOptions {
        jniLibs {
            pickFirsts += ['**/*.so']
        }
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.maarefa.moltaqa"
        minSdkVersion 26
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            // No signing config — builds unsigned APK
            shrinkResources false
            minifyEnabled false  // <== أوقف R8 مؤقتاً
            signingConfig signingConfigs.debug // حتى تبني بدون keystore

            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            // Use default debug signing
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    lint {
        abortOnError false
        checkReleaseBuilds false
        disable 'InvalidPackage', 'Instantiatable'
    }
}

flutter {
    source '../..'
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation ('org.jitsi.react:jitsi-meet-sdk:5.0.2') { transitive = true }
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation platform('com.google.firebase:firebase-bom:31.2.0')
    implementation 'androidx.appcompat:appcompat:1.6.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation 'androidx.work:work-runtime-ktx:2.8.1'
    implementation 'com.google.firebase:firebase-iid:21.1.0'
    implementation 'com.google.firebase:firebase-dynamic-links-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.android.gms:play-services-auth:20.5.0'
}
