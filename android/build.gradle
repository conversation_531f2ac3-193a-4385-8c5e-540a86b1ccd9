// allprojects {
//     repositories {
//         google()
//         mavenCentral()
//     }
// }

// rootProject.buildDir = '../build'
// subprojects {
//     project.buildDir = "${rootProject.buildDir}/${project.name}"
// }
// subprojects {
//     afterEvaluate { project ->
//         if (project.plugins.hasPlugin("com.android.application") ||
//                 project.plugins.hasPlugin("com.android.library")) {
//             project.android {
//                  if(namespace == null) {
//                 namespace project.group
//                 }
//                 compileSdkVersion 34
//                 buildToolsVersion "34.0.0"
//             }
//         }
//     }
// }

// subprojects {
//     project.evaluationDependsOn(':app')
// }

// tasks.register("clean", Delete) {
//     delete rootProject.buildDir
// }

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.4.0'
        classpath 'com.google.gms:google-services:4.4.1' // 🔥 Google Services Plugin
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.application") ||
                project.plugins.hasPlugin("com.android.library")) {
            project.android {
                if (namespace == null) {
                    namespace project.group
                }
                compileSdkVersion 34
                buildToolsVersion "34.0.0"
            }
        }
    }
}

subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
