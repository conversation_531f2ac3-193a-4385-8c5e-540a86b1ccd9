name: my_academy
description: A new Flutter project.

publish_to: "none" 

# ANDROID VERSION
# version: 2.0.5+31

# IOS VERSION
version: 3.0.12+1

environment:
  sdk: ">=3.0.5 <4.0.1"

dependencies:
  iconsax:
  table_calendar: ^3.2.0
  bloc: ^9.0.0
  cached_network_image: ^3.4.1
  card_swiper: ^3.0.1
  connectivity_plus: ^6.1.3
  cupertino_icons: ^1.0.2
  dartz: ^0.10.1
  date_time_picker: ^2.1.0
  device_preview: ^1.1.0
  dio: ^5.7.0
  dotted_border: ^2.0.0+3
  easy_localization: ^3.0.1
  equatable: ^2.0.5
  file_picker: ^9.0.0
  firebase_auth: ^5.3.4
  firebase_core: ^3.8.1
  firebase_dynamic_links: ^6.0.11
  firebase_messaging: ^15.1.6
  floating_bottom_navigation_bar: ^1.5.2
  pretty_dio_logger: ^1.4.0
  flutter:
    sdk: flutter
  flutter_bloc: ^9.0.0
  flutter_cached_pdfview: ^0.4.1
  flutter_html: null
  flutter_inappwebview: ^6.1.5
  flutter_launcher_icons: ^0.14.2
  flutter_local_notifications: ^18.0.1
  flutter_phoenix: ^1.1.0
  flutter_rating_bar: ^4.0.1
  flutter_screenutil: ^5.6.0
  flutter_svg: ^2.0.4
  flutter_widget_from_html: ^0.16.0
  fluttertoast: ^8.1.1
  geocoding: ^3.0.0
  geolocator: ^13.0.2
  get: ^4.6.5
  get_it: ^8.0.2
  google_fonts: ^6.2.1
  google_maps_flutter: ^2.10.0
  google_sign_in: ^6.2.2
  image_picker: ^1.1.2
  jitsi_meet_flutter_sdk: ^10.3.0
  # jitsi_meet_wrapper: ^0.2.0+1
  lottie: ^3.2.0
  overlay_support: ^2.1.0
  percent_indicator: ^4.2.2
  pin_code_fields: ^8.0.1
  rounded_loading_button_plus: ^3.0.1
  rxdart: ^0.28.0
  shared_preferences: ^2.0.18
  shimmer: ^3.0.0
  # social_share: ^2.3.1
  step_progress_indicator: ^1.0.2
  the_apple_sign_in: ^1.1.1
  url_launcher: ^6.1.6
  share_plus: ^10.1.4
  carousel_slider: ^5.0.0
  pdf: ^3.10.4
  path_provider: ^2.1.1
  # wakelock: ^0.6.2
#  iconsax_flutter: ^1.0.0

dependency_overrides:
  intl: ^0.17.0
  http: ^1.0.0
dev_dependencies:
  change_app_package_name: ^1.1.0
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

flutter_icons:
  android: "launcher_icon"
  ios: false
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/icon_release.png"
  image_path: "assets/images/icon_release.png"
  image_path_ios: "assets/images/icon_release.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/lotties/
    - assets/translate/
  fonts:
    - family: Shamel
      fonts:
        - asset: fonts/shamel.ttf
          weight: 400
