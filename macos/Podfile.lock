PODS:
  - connectivity_plus_macos (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - Firebase/Auth (10.3.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.3.0)
  - Firebase/CoreOnly (10.3.0):
    - FirebaseCore (= 10.3.0)
  - Firebase/Messaging (10.3.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.3.0)
  - firebase_auth (4.2.9):
    - Firebase/Auth (~> 10.3.0)
    - Firebase/CoreOnly (~> 10.3.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (2.7.0):
    - Firebase/CoreOnly (~> 10.3.0)
    - FlutterMacOS
  - firebase_messaging (14.2.5):
    - Firebase/CoreOnly (~> 10.3.0)
    - Firebase/Messaging (~> 10.3.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseAuth (10.3.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseCore (10.3.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.5.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.5.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.3.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - GoogleDataTransport (9.2.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.11.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.11.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.0)"
  - GoogleUtilities/Reachability (7.11.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (3.1.0)
  - location (0.0.1):
    - FlutterMacOS
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.2.0)
  - ReachabilitySwift (5.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus_macos (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus_macos/macos`)
  - firebase_auth (from `Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - location (from `Flutter/ephemeral/.symlinks/plugins/location/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GoogleDataTransport
    - GoogleUtilities
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus_macos/macos
  firebase_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  location:
    :path: Flutter/ephemeral/.symlinks/plugins/location/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus_macos: f6e86fd000e971d361e54b5afcadc8c8fa773308
  Firebase: f92fc551ead69c94168d36c2b26188263860acd9
  firebase_auth: 4e1927a3b9aabf27438ad431a3693e961b55056b
  firebase_core: 2569fb36c8e95ab963e04d8d9fdc9f0f1d297765
  firebase_messaging: 81bfa1d051f9f1f1cc77a7e1dea34f5356d13371
  FirebaseAuth: 0e415d29d846c1dce2fb641e46f35e9888d9bec6
  FirebaseCore: 988754646ab3bd4bdcb740f1bfe26b9f6c0d5f2a
  FirebaseCoreInternal: e463f41bb935cd049505bf7e9a5bdd7dcea90df6
  FirebaseInstallations: 935bc4abb6f7a035cab7a0c31cb777b2be3dd254
  FirebaseMessaging: e345b219fd15d325f0cf2fef28cb8ce00d851b3f
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  geolocator_apple: 72a78ae3f3e4ec0db62117bd93e34523f5011d58
  GoogleDataTransport: ea169759df570f4e37bdee1623ec32a7e64e67c4
  GoogleUtilities: c2bdc4cf2ce786c4d2e6b3bcfd599a25ca78f06f
  GTMSessionFetcher: c9e714f7eec91a55641e2bab9f45fd83a219b882
  location: 7cdb0665bd6577d382b0a343acdadbcb7f964775
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  path_provider_foundation: c68054786f1b4f3343858c1e1d0caaded73f0be9
  PromisesObjC: 09985d6d70fbe7878040aa746d78236e6946d2ef
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  shared_preferences_foundation: 986fc17f3d3251412d18b0265f9c64113a8c2472
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  url_launcher_macos: 5335912b679c073563f29d89d33d10d459f95451

PODFILE CHECKSUM: 353c8bcc5d5b0994e508d035b5431cfe18c1dea7

COCOAPODS: 1.12.0
