// import 'package:my_academy/models/common/specializations/specializations_model.dart';
//
// class ConsultationsModel {
//   ConsultationsModel({
//     this.id,
//     this.name,
//     this.specialization,
//   });
//
//   int? id;
//   String? name;
//   SpecializationsModel? specialization;
//
//   factory ConsultationsModel.fromJson(Map<String, dynamic> json) => ConsultationsModel(
//     id: json["id"],
//     name: json["name"],
//     specialization: SpecializationsModel.fromJson(json["specialization"]),
//   );
// }