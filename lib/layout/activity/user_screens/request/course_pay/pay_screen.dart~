// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:my_academy/layout/view/connectivity/connectivity_view.dart';
// import 'package:my_academy/widget/app_bar/default_app_bar/default_app_bar.dart';
//
// import '../../../../view/payment_method/payment_method_view.dart';
//
// class PayScreen extends StatelessWidget {
//   final int id;
//   final int? requestId;
//   final String type;
//   final bool isRequest;
//   const PayScreen(
//       {super.key,
//       required this.id,
//       required this.type,
//       this.isRequest = false,
//       this.requestId});
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: DefaultAppBar(title: tr("requset_summary")),
//       body: ConnectivityView(
//         child: PaymentMethodView(
//           isWallet: false,
//           isCourse: type == "course" ? true : false,
//           id: id,
//           // requestId: reuestId,
//           isRequest: isRequest,
//         ),
//       ),
//     );
//   }
// }

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:my_academy/layout/view/connectivity/connectivity_view.dart';
import 'package:my_academy/widget/app_bar/default_app_bar/default_app_bar.dart';

import '../../../../view/payment_method/payment_method_view.dart';

class PayScreen extends StatelessWidget {
  final int id;
  final int? requestId;
  final String type;
  final bool isRequest;

  const PayScreen({
    super.key,
    required this.id,
    required this.type,
    this.isRequest = false,
    this.requestId,
  });

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: Scaffold(
        backgroundColor: Colors.grey.shade50,
        appBar: _buildAppBar(context),
        body: ConnectivityView(
          child: Column(
            children: [
              _buildProgressIndicator(),
              Expanded(
                child: PaymentMethodView(
                  isWallet: false,
                  isCourse: type == "course",
                  id: id,
                  isRequest: isRequest,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.white,
      title: Text(
        _getScreenTitle(),
        style: TextStyle(
          color: Colors.grey.shade800,
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
      leading: IconButton(
        icon: Container(
          width: 40.w,
          height: 40.h,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.arrow_back_ios_new,
            color: Colors.grey.shade700,
            size: 18.sp,
          ),
        ),
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
      ),
      actions: [
        IconButton(
          icon: Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.help_outline,
              color: Colors.grey.shade700,
              size: 20.sp,
            ),
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            _showHelpDialog(context);
          },
        ),
        SizedBox(width: 8.w),
      ],
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1),
        child: Container(
          height: 1,
          color: Colors.grey.shade200,
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      color: Colors.white,
      child: Row(
        children: [
          _buildProgressStep(
            stepNumber: 1,
            title: tr("select_method"),
            isActive: true,
            isCompleted: false,
          ),
          Expanded(
            child: Container(
              height: 2.h,
              color: Colors.grey.shade300,
              margin: EdgeInsets.symmetric(horizontal: 8.w),
            ),
          ),
          _buildProgressStep(
            stepNumber: 2,
            title: tr("confirm_payment"),
            isActive: false,
            isCompleted: false,
          ),
          Expanded(
            child: Container(
              height: 2.h,
              color: Colors.grey.shade300,
              margin: EdgeInsets.symmetric(horizontal: 8.w),
            ),
          ),
          _buildProgressStep(
            stepNumber: 3,
            title: tr("complete"),
            isActive: false,
            isCompleted: false,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressStep({
    required int stepNumber,
    required String title,
    required bool isActive,
    required bool isCompleted,
  }) {
    Color getColor() {
      if (isCompleted) return Colors.green;
      if (isActive) return Theme.of(Get.context!).primaryColor;
      return Colors.grey.shade400;
    }

    return Column(
      children: [
        Container(
          width: 30.w,
          height: 30.h,
          decoration: BoxDecoration(
            color: isActive || isCompleted ? getColor() : Colors.transparent,
            shape: BoxShape.circle,
            border: Border.all(
              color: getColor(),
              width: 2,
            ),
          ),
          child: Center(
            child: isCompleted
                ? Icon(
              Icons.check,
              color: Colors.white,
              size: 16.sp,
            )
                : Text(
              stepNumber.toString(),
              style: TextStyle(
                color: isActive ? Colors.white : getColor(),
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          title,
          style: TextStyle(
            color: isActive ? getColor() : Colors.grey.shade600,
            fontSize: 10.sp,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ],
    );
  }

  String _getScreenTitle() {
    if (type == "course") {
      return tr("course_payment");
    } else if (isRequest) {
      return tr("request_payment");
    } else {
      return tr("lesson_payment");
    }
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Row(
          children: [
            Icon(
              Icons.help_outline,
              color: Theme.of(context).primaryColor,
            ),
            SizedBox(width: 8.w),
            Text(
              tr("payment_help"),
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHelpItem(
              icon: Icons.security,
              title: tr("secure_payment"),
              description: tr("all_payments_encrypted"),
            ),
            SizedBox(height: 12.h),
            _buildHelpItem(
              icon: Icons.support,
              title: tr("customer_support"),
              description: tr("contact_support_24_7"),
            ),
            SizedBox(height: 12.h),
            _buildHelpItem(
              icon: Icons.receipt,
              title: tr("instant_receipt"),
              description: tr("receive_receipt_immediately"),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              tr("got_it"),
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 32.w,
          height: 32.h,
          decoration: BoxDecoration(
            color: Theme.of(Get.context!).primaryColor.withValues(alpha:0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: 16.sp,
            color: Theme.of(Get.context!).primaryColor,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}