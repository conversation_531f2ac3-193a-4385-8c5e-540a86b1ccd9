import 'package:flutter/material.dart';

const Color mainColor = Color(0xffF19704);
const Color mainLightColor = Color(0xffEFB10B);
const Color headerColor = Color(0xffCA6815);
const Color timerColor = Color(0xffD7E6FD);
const Color secColor = Color(0xffEFB10B);
const Color cvBackgroundColor = Color(0xffF3FDFF);
const Color cvColor = Color(0xff3F2571);

const Color blackColor = Color(0xff212121);
const Color courseTypeColor = Color(0xffF1F9FF);
const Color subjectColor = Color(0xffECF7E8);
const Color inProgressColor = Color(0xff46B420);
const Color smallTextColor = Color(0xff6DD7FF);
const Color buttonColor = Color(0xff878787);

const Color inProgressBorderColor = Color(0xffCBF8BB);
const Color enterColor = Color(0xffFF6B8F);
const Color questionColor = Color(0xffD34B6C);
const Color profileBackgroundColor = Color(0xffE67625);
const Color borderColor = Color(0xffE8E8E8);
const Color profileColor = Color(0xffE2EDFD);

const Color filterColor = Color(0xff82ABE9);
const Color textfieldColor = Color(0xffBFCCD6);
const Color txtColor = Color(0xff3A192A);
const Color timeColor = Color(0xffD9C9BF);
const Color boxColor = Color(0xffEEF2FF);
// const Color shapeColor = Color(0xff46b4201a);
const Color circleColor = Color(0xFFFF003E);
const Color lightColor = Color(0xffFFF4EB);

const Color sufixtextColor = Color(0xff3C68A5);
const Color darkGrey = Color(0xff707070);
const Color white = Colors.white;
const Color black = Colors.black;
const Color grey = Colors.grey;
const Color red = Colors.red;
const Color transparent = Colors.transparent;
const Color semiGray = Color(0xFFFAFAFA);
Color discountCardColor = const Color(0xFF5079D8).withValues(alpha: 0.1);
const Color profileIconCardColor = Color(0xFFAFCDFB);
const Color profileCardColor = Color(0xFFF8FAFB);
const Color profileBorderCardColor = Color(0xFFBFCCD6);

LinearGradient blueGradient = const LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      mainColor,
      secColor,
    ]);

LinearGradient dateGradient = const LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      mainColor,
      profileColor,
    ]);

LinearGradient filterGradient = const LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      mainColor,
      filterColor,
    ]);
// new colors
const greyColor = Color(0xffBEBEBE);
const primaryText = Color(0xff272727);
const accentColor = Color(0xff3F2571);
const Color scaffoldBackgroundColor = Color(0xFFF9FAFB);
