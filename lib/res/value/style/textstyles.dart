import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../color/color.dart';

abstract class TextStyles {
  static TextStyle headerStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 29.sp,
      color: secColor,
      fontWeight: FontWeight.bold);
  static TextStyle priceStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 28.sp,
      color: blackColor,
      fontWeight: FontWeight.bold);
  static TextStyle loginTitleStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 28.sp,
      color: blackColor,
      fontWeight: FontWeight.bold);
  static TextStyle selectedStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 20.sp,
      color: mainColor,
      fontWeight: FontWeight.bold);
  static TextStyle introStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 22.sp,
      color: white,
      fontWeight: FontWeight.w700);
  static TextStyle introStyle2 = TextStyle(
      fontFamily: "Shamel",
      fontSize: 16.sp,
      color: white,
      fontWeight: FontWeight.w700);
  static TextStyle roleStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 22.sp,
      color: white,
      fontWeight: FontWeight.w700);
  static TextStyle titleStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 17.sp,
      color: questionColor,
      fontWeight: FontWeight.bold);
  static TextStyle appBarStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 18.sp,
      color: headerColor,
      fontWeight: FontWeight.w700);
  static TextStyle textView20Bold = TextStyle(
      fontFamily: "Shamel",
      fontSize: 20.sp,
      color: mainColor,
      fontWeight: FontWeight.bold);
  static TextStyle textView16Regular =
      TextStyle(fontFamily: "Shamel", fontSize: 16.sp, color: grey);
  static TextStyle textView16SemiBold = TextStyle(
      fontFamily: "Shamel",
      fontSize: 16.sp,
      color: blackColor,
      fontWeight: FontWeight.w700);
  static TextStyle textView16Bold = TextStyle(
      fontFamily: "Shamel",
      fontSize: 16.sp,
      color: blackColor,
      fontWeight: FontWeight.bold);
  static TextStyle textView14Bold = TextStyle(
      fontFamily: "Shamel",
      fontSize: 14.sp,
      color: blackColor,
      fontWeight: FontWeight.bold);
  static TextStyle agreeStyle =
      TextStyle(fontFamily: "Shamel", fontSize: 18.sp, color: grey);
  static TextStyle unselectedStyle =
      TextStyle(fontFamily: "Shamel", fontSize: 16.sp, color: grey);
  static TextStyle subTitleStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 14.sp,
      color: mainColor,
      fontWeight: FontWeight.bold);
  static TextStyle subTitleStyle2 = TextStyle(
      fontFamily: "Shamel",
      fontSize: 16.sp,
      color: mainColor,
      fontWeight: FontWeight.bold);
  static TextStyle hintStyle = TextStyle(
    fontFamily: "Shamel",
    fontSize: 14.sp,
    color: grey,
  );
  static TextStyle textView14SemiBold = TextStyle(
    fontFamily: "Shamel",
    fontSize: 14.sp,
    color: mainColor,
    fontWeight: FontWeight.w700,
  );
  static TextStyle errorStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 12.sp,
      color: red,
      fontWeight: FontWeight.w700);
  static TextStyle contentStyle = TextStyle(
    fontFamily: "Shamel",
    fontSize: 12.sp,
    color: grey,
  );
  static TextStyle textView12Bold = TextStyle(
      fontFamily: "Shamel",
      fontSize: 12.sp,
      color: enterColor,
      fontWeight: FontWeight.bold);
  static TextStyle textView17Bold = TextStyle(
      fontFamily: "Shamel",
      fontSize: 17.sp,
      color: mainColor,
      fontWeight: FontWeight.bold);
  static TextStyle smallStyle = TextStyle(
      fontFamily: "Shamel",
      fontSize: 10.sp,
      color: secColor,
      fontWeight: FontWeight.w700);
}
