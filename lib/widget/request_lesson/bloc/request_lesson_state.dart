// part of 'request_lesson_cubit.dart';
//
// @immutable
// sealed class RequestLessonState {}
//
// final class RequestLessonInitial extends RequestLessonState {}
//
// // final class MakeCouponLoadingState extends RequestLessonState {}
// //
// // final class MakeCouponSuccessState extends RequestLessonState {
// //   final CouponResponseDataModel couponResponseDataModel;
// //   MakeCouponSuccessState({required this.couponResponseDataModel});
// // }
// //
// // final class MakeCouponErrorState extends RequestLessonState {}