<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="192" height="185.612" viewBox="0 0 192 185.612">
  <defs>
    <linearGradient id="linear-gradient" x1="1.508" y1="0.5" x2="-0.533" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ca6815"/>
      <stop offset="1" stop-color="#f8a200"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-1.4" y1="-0.882" x2="0.954" y2="0.74" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffc620"/>
      <stop offset="0.33" stop-color="#ffbe16"/>
      <stop offset="1" stop-color="#ffaf00"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="1.26" y1="0.499" x2="-0.387" y2="0.499" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="4.938" y1="0.502" x2="-1.318" y2="0.502" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-5" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e89e20"/>
      <stop offset="1" stop-color="#ffaf00"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="1.037" y1="0.691" x2="0.077" y2="0.19" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-7" x1="0.013" y1="0.319" x2="0.833" y2="0.633" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffd420"/>
      <stop offset="1" stop-color="#ffaf00"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="-0.345" y1="-0.277" x2="0.737" y2="0.573" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-9" x1="-0.099" y1="0.027" x2="1.287" y2="1.238" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-10" x1="1.048" y1="0.273" x2="-0.1" y2="0.627" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#474646"/>
      <stop offset="1" stop-color="#6c7072"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="1.061" y1="0.27" x2="-0.088" y2="0.623" xlink:href="#linear-gradient-10"/>
    <linearGradient id="linear-gradient-12" x1="1.259" y1="0.291" x2="-0.259" y2="0.619" xlink:href="#linear-gradient-10"/>
    <linearGradient id="linear-gradient-13" x1="0.23" y1="-0.766" x2="0.849" y2="2.065" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#6c7072"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="0.007" y1="-1.776" x2="0.627" y2="1.047" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-15" x1="-0.788" y1="-4.171" x2="0.269" y2="-0.382" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-16" x1="0.446" y1="0.996" x2="0.6" y2="0.363" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffc620"/>
      <stop offset="0.14" stop-color="#ffc724"/>
      <stop offset="0.27" stop-color="#ffca30"/>
      <stop offset="0.41" stop-color="#ffcf43"/>
      <stop offset="0.54" stop-color="#ffd65f"/>
      <stop offset="0.67" stop-color="#ffdf82"/>
      <stop offset="0.8" stop-color="#ffeaae"/>
      <stop offset="0.93" stop-color="#fff7e1"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
  </defs>
  <g id="logo" transform="translate(-2233.99 -254.32)">
    <g id="OBJECTS" transform="translate(2233.99 254.32)">
      <path id="Path_159" data-name="Path 159" d="M2365.211,423.152c-.493-.135,22.994-4.382,22.994-4.382,48.332,14.6,32.708,70.382,32.708,70.382S2404.336,434.213,2365.211,423.152Z" transform="translate(-2323.321 -387.034)" fill="#dce4e6"/>
      <path id="Path_160" data-name="Path 160" d="M2310.169,448.08l74.253,55.5,74.313-62.872,6.581,8.035c-11.427,8.774-45.106,39.381-45.106,39.381l47.992-35.091,7.989,8.439c-3.471,2.742-70.3,56.794-90.993,59.048-10.224,1.115-83.469-56-87.688-59.02Z" transform="translate(-2287.086 -398.778)" fill="#dce4e6"/>
      <path id="Path_161" data-name="Path 161" d="M2336.276,446.178l27.149,19.621c-.348-.576-13.477-12.91-21.749-20.662l11.809-2.277a82.212,82.212,0,0,1,16.73,13.305c16.158,17.432,30.8,46.091,30.8,46.091s-11.618-8.625-15.824-12.055-19.021-14.871-31.6-24.477-23.752-18.31-23.752-18.31Z" transform="translate(-2304.392 -399.929)" fill="#f4ad0f"/>
      <path id="Path_162" data-name="Path 162" d="M2360.282,541.576a8.438,8.438,0,0,1-.655-.929c-.553-.813-.855-1.357-.855-1.357l-83.692-54.563,3.253-5.01,87.679,57.161a15.525,15.525,0,0,0,4.513,1.775,16,16,0,0,0,10.521-1.859c.112-.07.218-.121.325-.2v0l82.6-57.974,3.108,4.489-63.365,45.115h0l-15.006,10.531s-4.3,10.34-14.811,10.568c-5.823.1-10.135-3.6-12.645-6.539-.274-.321-.549-.66-.818-1.008Z" transform="translate(-2275.08 -419.07)" fill="#3d2572"/>
      <path id="Path_163" data-name="Path 163" d="M2512.921,444.081a3.61,3.61,0,0,1,.636-.86,7.069,7.069,0,0,1,.72-.386,1.8,1.8,0,0,0,.971-1.362c0-.507-.39-1.022-1.213-1.51-.437.344-.823,1.018-1.417.995-.465,0-.888-.367-1.324-.5-4.341-1.3-13.83-2.547-19.1-1.929-3.6.669-2.673,1.738-2.673,2.9a1.168,1.168,0,0,0,.047.321,40.482,40.482,0,0,1,14.727,3.829,30.1,30.1,0,0,1,7.236,5.21,6.046,6.046,0,0,0,.326-2.551A8.092,8.092,0,0,1,2512.921,444.081Z" transform="translate(-2389.806 -397.525)" fill="url(#linear-gradient)"/>
      <g id="Group_17" data-name="Group 17" transform="translate(88.362 0)">
        <path id="Path_164" data-name="Path 164" d="M2451.64,352.32Z" transform="translate(-2438.855 -306.777)" fill="#fff"/>
        <path id="Path_165" data-name="Path 165" d="M2513.211,462.058a13.6,13.6,0,0,0,4.3-1.153c1.078-.581.865-2.128.93-3.137a14.5,14.5,0,0,1,.158-1.6c-4.647,3.908-10.688,4.973-16.9,5.219A29.477,29.477,0,0,0,2513.211,462.058Z" transform="translate(-2484.747 -407.053)" fill="url(#linear-gradient-2)"/>
        <path id="Path_166" data-name="Path 166" d="M2518.459,358.622a18.409,18.409,0,0,0-5.633-4.977,24.55,24.55,0,0,0-3.118-1.487c-1.343-.493-1.395-.511-2.231-.753-1.106-.293-1.106-.293-1.7-.418-.929-.177-.929-.177-1.395-.246-.8-.107-.8-.107-1.2-.144-.734-.065-.734-.065-1.1-.084-.707-.028-.707-.028-1.055-.033h-1.064c-.744.037-.744.037-1.12.07-.809.074-.823.079-1.25.135a25.248,25.248,0,0,0-5.958,1.589,26.952,26.952,0,0,0-3.923,2.045l-1.268.874c4.034-2.449,23.2-2.133,32.014,3.416" transform="translate(-2476.578 -350.48)" fill="url(#linear-gradient-3)"/>
        <path id="Path_167" data-name="Path 167" d="M2487.765,443.137c-.892-.507-1.315-1.046-1.315-1.575,0-1.162,2.05-2.277,5.646-2.946-6.469-.079-9.262.9-8.221,1.9h0c.349.27.809.637,1.167.9a24.1,24.1,0,0,0,2.324,1.487l.553.3-.149-.079" transform="translate(-2475.091 -397.654)" fill="url(#linear-gradient-4)"/>
        <path id="Path_168" data-name="Path 168" d="M2554.953,376.143c1.6,1.738,2.216,2.556,2.268,4.28l.023-.957a22.967,22.967,0,0,0-.34-4.085,22.475,22.475,0,0,0-.99-3.88,23.286,23.286,0,0,0-1.6-3.62,22.914,22.914,0,0,0-2.166-3.3,24.135,24.135,0,0,0-2.7-2.942c-.753-.646-1.78-1.473-2.575-2.063l-.381-.251a20.452,20.452,0,0,1,8.463,16.823" transform="translate(-2508.722 -355.212)" fill="url(#linear-gradient-5)"/>
        <path id="Path_169" data-name="Path 169" d="M2511.141,388.925c.144-2.091.144-4.215.144-6.255a23.7,23.7,0,0,0-.553-5.214c-12.278-8.486-39.831-7.519-42.132-4.145l-.381.669a22.91,22.91,0,0,0-1.613,3.616,22.663,22.663,0,0,0-1,3.88,23.614,23.614,0,0,0-.353,4.08c0,.627.042,1.459.084,2.082a23.156,23.156,0,0,0,.362,2.607,22.627,22.627,0,0,0,.632,2.519c.209.613.492,1.422.73,2.026-.809-4.4,30.5.548,37.224,7.436a13.308,13.308,0,0,1-3.155,1.6c.822.488,1.213,1,1.213,1.51,0,.581-.511,1.153-1.468,1.659a24.1,24.1,0,0,0,2.365-1.436,23.785,23.785,0,0,0,4-3.43,24.106,24.106,0,0,0,1.664-1.97,22.743,22.743,0,0,0,1.431-2.124l.507-.883C2512.68,392.09,2511.992,389.919,2511.141,388.925Z" transform="translate(-2465.236 -361.385)" fill="url(#linear-gradient-6)"/>
        <g id="Group_16" data-name="Group 16" transform="translate(0 4.359)">
          <path id="Path_170" data-name="Path 170" d="M2505.943,361.923a24.247,24.247,0,0,1,2.705,2.942,23.521,23.521,0,0,1,2.165,3.3,22.635,22.635,0,0,1,1.6,3.62,23.006,23.006,0,0,1,.99,3.88,23.784,23.784,0,0,1,.339,4.085l-.023.957c-1.6,11.409-42.6,13.147-46.51,8.477a2.855,2.855,0,0,1-.181-.284c-.232-.6-.525-1.394-.729-2.026a23.217,23.217,0,0,1-.632-2.519,23.8,23.8,0,0,1-.363-2.607c-.032-.623-.074-1.455-.084-2.082a23.007,23.007,0,0,1,.353-4.08,22.752,22.752,0,0,1,1-3.88,23.249,23.249,0,0,1,1.613-3.616l.381-.669c-1.487,4.062,22.725,4.568,33.056-.572,4.285-2.138,4.94-4.749,1.743-6.994a32.823,32.823,0,0,1,2.574,2.063" transform="translate(-2465.219 -359.86)" fill="url(#linear-gradient-7)"/>
          <path id="Path_171" data-name="Path 171" d="M2520.409,428.323a24.141,24.141,0,0,1-1.431,2.124,23.417,23.417,0,0,1-1.664,1.971,23.785,23.785,0,0,1-4,3.43,24.148,24.148,0,0,1-2.366,1.436,18.168,18.168,0,0,1-5.735,1.538c-6.283.846-13.849.135-17.065-1.585l-.553-.3a23.939,23.939,0,0,1-2.323-1.487c-.353-.27-.823-.623-1.167-.9,4.122,2.714,31.536,1.455,36.787-7.106l-.506.883" transform="translate(-2475.331 -396.034)" fill="url(#linear-gradient-8)"/>
        </g>
      </g>
      <path id="Path_172" data-name="Path 172" d="M2506.691,352.64a18.787,18.787,0,0,0-4.238-1.394c1.306.562.27,1.352-2.324,1.585a19.642,19.642,0,0,1-7.264-.6c-1.138-.465-.557-.929,1.152-1.231-.9.107-1.728.2-2.245.311a23.84,23.84,0,0,0-4.233,1.394,24.885,24.885,0,0,0-3.541,1.929,24.191,24.191,0,0,0-3.062,2.393l-.614.59-.093.237c1.1,1.859,18.124,2.091,25.834-.776C2507.76,356.47,2511.259,354.755,2506.691,352.64Z" transform="translate(-2384.892 -350.758)" fill="url(#linear-gradient-9)"/>
      <path id="Path_173" data-name="Path 173" d="M2515.8,475.349a40.427,40.427,0,0,1-15.424,0,1.682,1.682,0,0,1-1.348-1.6h0a1.111,1.111,0,0,1,1.347-1.12,40.43,40.43,0,0,0,15.424,0,1.115,1.115,0,0,1,1.352,1.12h0A1.687,1.687,0,0,1,2515.8,475.349Z" transform="translate(-2394.955 -415.851)" fill="url(#linear-gradient-10)"/>
      <path id="Path_174" data-name="Path 174" d="M2515.8,483.549a40.431,40.431,0,0,1-15.424,0,1.678,1.678,0,0,1-1.348-1.594h0a1.111,1.111,0,0,1,1.348-1.125,40.43,40.43,0,0,0,15.424,0,1.115,1.115,0,0,1,1.352,1.125h0a1.683,1.683,0,0,1-1.352,1.594Z" transform="translate(-2394.954 -420.24)" fill="url(#linear-gradient-11)"/>
      <path id="Path_175" data-name="Path 175" d="M2503.78,489.36a5.474,5.474,0,0,0,4.712,3.695,38.672,38.672,0,0,0,4.28,0,5.474,5.474,0,0,0,4.712-3.695A41.362,41.362,0,0,1,2503.78,489.36Z" transform="translate(-2397.498 -424.819)" fill="url(#linear-gradient-12)"/>
      <g id="Group_18" data-name="Group 18" transform="translate(105.951 57.602)" opacity="0.63">
        <path id="Path_176" data-name="Path 176" d="M2509.156,476a27.949,27.949,0,0,1-5.86-.706.465.465,0,0,1,.209-.864,25.364,25.364,0,0,0,7.222.609.465.465,0,0,1,.1.883A16.549,16.549,0,0,1,2509.156,476Z" transform="translate(-2503.067 -474.43)" fill="url(#linear-gradient-13)"/>
        <path id="Path_177" data-name="Path 177" d="M2509.156,483.745a27.8,27.8,0,0,1-5.86-.711.465.465,0,0,1,.209-.864,25.362,25.362,0,0,0,7.222.609.465.465,0,0,1,.1.883A16.59,16.59,0,0,1,2509.156,483.745Z" transform="translate(-2503.067 -478.573)" fill="url(#linear-gradient-14)"/>
        <path id="Path_178" data-name="Path 178" d="M2511.557,491.948a24.866,24.866,0,0,1-3.644-.293.444.444,0,1,1,.13-.878,23.542,23.542,0,0,0,3.88.279h0a.465.465,0,0,1,0,.888Z" transform="translate(-2505.458 -483.179)" fill="url(#linear-gradient-15)"/>
      </g>
      <g id="Group_19" data-name="Group 19" transform="translate(91.788 1.329)" opacity="0.22">
        <path id="Path_179" data-name="Path 179" d="M2505.711,370.8c2.975-1.482,4.183-3.2,3.6-4.861.079,1.441-1.171,2.891-3.718,4.183-9.574,4.735-30.951,4.675-32.982,1.422C2471.852,375.448,2495.553,375.871,2505.711,370.8Z" transform="translate(-2472.592 -360.084)" fill="#fff"/>
        <path id="Path_180" data-name="Path 180" d="M2519.1,430.317a19.958,19.958,0,0,0,1.333-1.994l.507-.883c-5.251,8.56-32.665,9.82-36.787,7.106.344.279.813.632,1.167.9l.586.414C2492.366,437.492,2511.764,436.251,2519.1,430.317Z" transform="translate(-2478.778 -393.004)" fill="#fff"/>
        <path id="Path_181" data-name="Path 181" d="M2507.005,354.186a19.433,19.433,0,0,0,7.263.6c2.4-.214,3.467-.9,2.6-1.445a5.343,5.343,0,0,1-2.742.753,19.448,19.448,0,0,1-7.263-.6l-.209-.1C2506.266,353.647,2506.341,353.912,2507.005,354.186Z" transform="translate(-2490.698 -353.34)" fill="#fff"/>
      </g>
      <path id="Path_182" data-name="Path 182" d="M2523.514,384.416a23,23,0,0,0-.99-3.88,22.683,22.683,0,0,0-1.6-3.62c-.158-.288-.284-.511-.4-.716-6.441,12.431-21.424,20.025-36.415,23.812,12.775,1.417,38.456-1.571,39.719-10.554l.023-.957A23.758,23.758,0,0,0,2523.514,384.416Z" transform="translate(-2386.969 -364.247)" opacity="0.6" fill="url(#linear-gradient-16)"/>
      <path id="Path_183" data-name="Path 183" d="M2368.725,638.912h6.892q1.548-.06,1.589,1.752v10.08h-.869l-.028-4.721v-5.461c0-.465-.167-.734-.562-.734h-7.082c-.581,0-.869.279-.869.841v8.17a.832.832,0,0,0,.929.929h3.365a1.059,1.059,0,0,0,.9-.72l.367-.725.678-1.445,1.431-2.891h.971l-2.565,5.275a2.067,2.067,0,0,1-2.082,1.394h-3.169a1.659,1.659,0,0,1-1.743-1.636v-8.4A1.616,1.616,0,0,1,2368.725,638.912Zm2.951-4.183v1.213h-1.417V634.73Zm2.226,0v1.213h-1.389V634.73Z" transform="translate(-2324.219 -502.632)" fill="#3d2572"/>
      <path id="Path_184" data-name="Path 184" d="M2387.26,649.85h12.115a.585.585,0,0,0,.66-.665v-1.018l-7.4-1.018a1.746,1.746,0,0,1-1.729-1.817v-4.88q0-1.52,1.71-1.52h6.608q1.785,0,1.8,1.859v8.277a1.484,1.484,0,0,1-1.655,1.7H2387.33Zm12.761-2.6v-6.743c0-.465-.247-.664-.739-.664h-6.934c-.381,0-.572.181-.572.534v4.94c0,.5.362.836,1.092,1Zm-3.434-12.482v1.213h-1.394V634.77Z" transform="translate(-2335.127 -502.654)" fill="#3d2572"/>
      <path id="Path_185" data-name="Path 185" d="M2422.083,643.88v12.083a2.209,2.209,0,0,1-.427,1.445l-2.124,2.644h-1.092l2.323-2.951a1.619,1.619,0,0,0,.414-1.125V643.894Z" transform="translate(-2351.817 -507.53)" fill="#3d2572"/>
      <path id="Path_186" data-name="Path 186" d="M2430.034,643.73h8.393a1.45,1.45,0,0,1,1.622,1.575v5.372a4.287,4.287,0,0,1-.767,2.324,4.965,4.965,0,0,1-1.947,1.673h2.756v.883H2425.22l.033-.9h7.765l-4.749-6.041v-3.3Q2428.264,643.73,2430.034,643.73Zm-.929,1.547v3.039l5.033,6.329h.739a5.512,5.512,0,0,0,3-1.441,3.494,3.494,0,0,0,1.236-2.528v-5.535c0-.353-.237-.53-.678-.53h-8.472c-.549,0-.841.223-.841.665Z" transform="translate(-2355.446 -507.45)" fill="#3d2572"/>
      <path id="Path_187" data-name="Path 187" d="M2470.32,645.57v8.277a1.605,1.605,0,0,1-1.785,1.7h-3.253c-.864,0-1.547-.558-2.049-1.71l-2.375-6.506h1.032l2.524,6.683a1.138,1.138,0,0,0,1.161.623h3.086a.7.7,0,0,0,.767-.753v-8.365c0-.581-.339-.874-1.05-.874h-6.562a.688.688,0,0,0-.781.771V655.6h-5v-.957h4.118v-9.03a1.538,1.538,0,0,1,1.724-1.887h6.831C2469.762,643.776,2470.3,644.395,2470.32,645.57Z" transform="translate(-2371.938 -507.45)" fill="#3d2572"/>
      <path id="Path_188" data-name="Path 188" d="M2506.345,638.912h6.887c1.032-.042,1.566.544,1.594,1.752v10.08h-.869l-.032-4.721v-5.461c0-.465-.167-.734-.562-.734h-7.078c-.581,0-.874.279-.874.841v8.17a.837.837,0,0,0,.929.929h3.36a1.056,1.056,0,0,0,.9-.72l.367-.725.679-1.445,1.431-2.891h.976l-2.561,5.275a2.074,2.074,0,0,1-2.083,1.394h-3.174a1.657,1.657,0,0,1-1.738-1.636v-8.4A1.619,1.619,0,0,1,2506.345,638.912Zm2.951-4.183v1.213H2507.9V634.73Zm2.226,0v1.213h-1.394V634.73Z" transform="translate(-2397.883 -502.632)" fill="#3d2572"/>
      <path id="Path_189" data-name="Path 189" d="M2533.96,645.608a2.3,2.3,0,0,1,2.2-1.891h6.567a1.526,1.526,0,0,1,1.724,1.682v8.611q-.056,1.394-1.785,1.473h-12.547v-2.714a4.4,4.4,0,0,1-1.794,1.966,5.053,5.053,0,0,1-2.631.734h-.683v-.9h.767q4.308-.163,4.322-6.594V643.6h.883v10.977Zm9.694,8.114v-8.467a.771.771,0,0,0-.813-.725h-6.325a1.455,1.455,0,0,0-1.608,1.06l-.562,1.668-2.421,7.347h10.786C2543.338,654.539,2543.654,654.251,2543.654,653.722Z" transform="translate(-2408.862 -507.38)" fill="#3d2572"/>
      <path id="Path_190" data-name="Path 190" d="M2570.3,638.943h.93v10.089a1.546,1.546,0,0,1-1.506,1.71h-5.354v-.929h4.94c.6,0,.929-.251.985-.785Zm.386-4.182v1.213h-1.394V634.76Z" transform="translate(-2429.93 -502.649)" fill="#3d2572"/>
      <path id="Path_191" data-name="Path 191" d="M2592.276,645.57v8.277a1.605,1.605,0,0,1-1.785,1.7h-3.253c-.864,0-1.547-.558-2.049-1.71l-2.375-6.506h1.031l2.523,6.683a1.148,1.148,0,0,0,1.166.623h3.081a.7.7,0,0,0,.767-.753v-8.365c0-.581-.339-.874-1.046-.874h-6.576a.688.688,0,0,0-.781.771V655.6H2578v-.957h4.113v-9.03a1.54,1.54,0,0,1,1.728-1.887h6.827C2591.714,643.776,2592.252,644.395,2592.276,645.57Z" transform="translate(-2437.226 -507.45)" fill="#3d2572"/>
      <text id="Maarefa" transform="translate(35.779 176.612)" fill="#3d2572" font-size="35" font-family="SegoeUI, Segoe UI"><tspan x="0" y="0">Maarefa</tspan></text>
    </g>
  </g>
</svg>
